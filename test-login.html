<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
</head>
<body>
    <h1>Creating Test Session...</h1>
    <p>This page will create a test user session and redirect you to the app.</p>
    
    <script>
        // Create a test user session
        const testUser = {
            id: 'test-user-123',
            username: 'TestUser',
            email: '<EMAIL>',
            bio: 'Test user for authentication',
            followers: 0,
            following: 0,
            stories: 0
        };
        
        localStorage.setItem('currentUser', JSON.stringify(testUser));
        console.log('Test user session created:', testUser);
        
        // Redirect to app after a short delay
        setTimeout(() => {
            window.location.href = '/app';
        }, 1000);
    </script>
</body>
</html>
