// Firebase Configuration
// Replace these values with your actual Firebase project configuration
// You can find these values in your Firebase Console > Project Settings > General > Your apps

const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "123456789012",
    appId: "1:123456789012:web:abcdef123456789012345678"
};

// Instructions:
// 1. Go to https://console.firebase.google.com/
// 2. Select your project
// 3. Click on "Project Settings" (gear icon)
// 4. Scroll down to "Your apps" section
// 5. If you don't have a web app, click "Add app" and select the web icon (</>)
// 6. Copy the configuration object and replace the values above
// 7. Make sure Authentication is enabled in your Firebase Console
// 8. Enable Email/Password authentication in Authentication > Sign-in method

export default firebaseConfig;
